<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>H5 预览服务</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
      }

      .container {
        text-align: center;
        max-width: 600px;
        padding: 40px 20px;
      }

      .logo {
        font-size: 48px;
        margin-bottom: 20px;
      }

      .title {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .subtitle {
        font-size: 18px;
        opacity: 0.9;
        margin-bottom: 40px;
        line-height: 1.6;
      }

      .info-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .info-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .info-content {
        font-size: 16px;
        line-height: 1.6;
        opacity: 0.9;
      }

      .url-example {
        background: rgba(0, 0, 0, 0.2);
        padding: 12px 16px;
        border-radius: 8px;
        font-family: "Monaco", "Menlo", monospace;
        font-size: 14px;
        margin-top: 12px;
        word-break: break-all;
      }

      .status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-top: 20px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        background: #4ade80;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .footer {
        margin-top: 40px;
        font-size: 14px;
        opacity: 0.7;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">🎨</div>
      <h1 class="title">H5 预览服务</h1>
      <p class="subtitle">
        专为海报作品设计的独立预览服务<br />
        完美还原编辑器中的 Canvas 效果
      </p>

      <div class="info-card">
        <div class="info-title">📱 预览地址格式</div>
        <div class="info-content">
          访问以下格式的URL来预览您的作品：
          <div class="url-example">
            http://localhost:8082/p/preview/{workId}-{uuid}
          </div>
        </div>
      </div>

      <div class="info-card">
        <div class="info-title">🚀 功能特性</div>
        <div class="info-content">
          • Canvas 效果完美还原<br />
          • 响应式移动端适配<br />
          • 现代化预览界面<br />
          • 独立部署支持
        </div>
      </div>

      <div class="info-card">
        <div class="info-title">🔧 API 接口</div>
        <div class="info-content">
          获取作品数据：
          <div class="url-example">GET /api/work/{workId}</div>
        </div>
      </div>

      <div class="status">
        <div class="status-dot"></div>
        <span>服务运行正常</span>
      </div>

      <div class="footer">Powered by Node.js + Express</div>
    </div>
  </body>
</html>
