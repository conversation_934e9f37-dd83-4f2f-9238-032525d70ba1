<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>海报预览</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        background: #f5f5f5;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .canvas-area {
        position: relative;
        width: 375px;
        background: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .component-item {
        position: absolute;
        cursor: default;
      }

      .l-text {
        font-family: inherit;
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.4;
      }

      .l-image {
        max-width: 100%;
        height: auto;
        display: block;
      }

      .loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #6c757d;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e9ecef;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        text-align: center;
        padding: 40px 20px;
        color: #dc3545;
      }

      .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .error-message {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .error-detail {
        font-size: 14px;
        color: #6c757d;
      }

      /* 顶部刘海提示条样式 */
      .canvas-area-header {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 34px;
        color: #ffffff;
        background: red;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        z-index: 2;
      }
    </style>
  </head>
  <body>
    <div id="loading" class="loading">
      <div class="loading-spinner"></div>
      <div>正在加载...</div>
    </div>

    <div id="error" class="error" style="display: none">
      <div class="error-icon">⚠️</div>
      <div class="error-message">加载失败</div>
      <div class="error-detail">请检查网络连接或稍后重试</div>
    </div>

    <div id="canvas-area" class="canvas-area" style="display: none">
      <!-- 组件内容将在这里动态生成 -->
    </div>

    <script>
      // 从URL获取作品ID和UUID
      function getWorkInfoFromURL() {
        const path = window.location.pathname;
        // 支持 /p/preview/:workId-:uuid 格式
        const match = path.match(/\/p\/preview\/(\d+)-(.+)/);
        if (match) {
          return {
            workId: match[1],
            uuid: match[2],
          };
        }
        return null;
      }

      // 加载作品数据
      async function loadWorkData() {
        const workInfo = getWorkInfoFromURL();
        if (!workInfo) {
          showError("无效的预览链接");
          return;
        }

        try {
          const response = await fetch(`/api/work/${workInfo.workId}`);
          const result = await response.json();

          if (result.errno === 0 && result.data) {
            renderWork(result.data);
          } else {
            showError(result.message || "作品不存在");
          }
        } catch (error) {
          console.error("加载作品数据失败:", error);
          showError("网络错误，请稍后重试");
        }
      }

      // 渲染作品
      function renderWork(workData) {
        const { content } = workData;
        const { components = [], props = {} } = content || {};

        // 设置canvas区域样式
        const canvasArea = document.getElementById("canvas-area");
        canvasArea.style.height = props.height || "560px";
        canvasArea.style.backgroundColor = props.backgroundColor || "#ffffff";

        if (props.backgroundImage) {
          canvasArea.style.backgroundImage = `url(${props.backgroundImage})`;
          canvasArea.style.backgroundSize = "cover";
          canvasArea.style.backgroundPosition = "center";
        }

        // 渲染组件
        // 保留顶部提示条，防止被覆盖
        const headerHTML =
          canvasArea.querySelector(".canvas-area-header")?.outerHTML ||
          '<div class="canvas-area-header">仅供预览，请发布作品后，再正式使用</div>';

        canvasArea.innerHTML = `${headerHTML}\n${generateComponentsHTML(
          components
        )}`;

        // 隐藏加载状态，显示内容
        document.getElementById("loading").style.display = "none";
        canvasArea.style.display = "block";
      }

      // 显示错误
      function showError(message) {
        document.getElementById("loading").style.display = "none";
        const errorEl = document.getElementById("error");
        errorEl.style.display = "block";
        errorEl.querySelector(".error-message").textContent = message;
      }

      // 生成组件HTML
      function generateComponentsHTML(components) {
        return components
          .map((component) => {
            const { name, props = {} } = component;

            // 构建样式
            const styles = [];
            Object.keys(props).forEach((key) => {
              const value = props[key];
              if (typeof value === "string" && value) {
                // 处理CSS属性
                if (
                  [
                    "fontSize",
                    "width",
                    "height",
                    "top",
                    "left",
                    "right",
                    "bottom",
                  ].includes(key)
                ) {
                  styles.push(
                    `${kebabCase(key)}: ${
                      value.includes("px") ? value : value + "px"
                    }`
                  );
                } else if (
                  ["color", "backgroundColor", "borderColor"].includes(key)
                ) {
                  styles.push(`${kebabCase(key)}: ${value}`);
                } else if (
                  [
                    "fontWeight",
                    "textAlign",
                    "position",
                    "textDecoration",
                    "fontStyle",
                  ].includes(key)
                ) {
                  styles.push(`${kebabCase(key)}: ${value}`);
                }
              }
            });

            const styleStr = styles.join("; ");

            // 根据组件类型生成HTML
            if (name === "l-text") {
              return `<div class="component-item l-text" style="${styleStr}">${
                props.text || ""
              }</div>`;
            } else if (name === "l-image") {
              return `<div class="component-item" style="${styleStr}">
                              <img class="l-image" src="${
                                props.src || ""
                              }" alt="${props.alt || ""}" />
                            </div>`;
            }

            return "";
          })
          .join("\n");
      }

      // 将驼峰命名转换为短横线命名
      function kebabCase(str) {
        return str.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
      }

      // 页面加载完成后开始加载数据
      document.addEventListener("DOMContentLoaded", loadWorkData);
    </script>
  </body>
</html>
