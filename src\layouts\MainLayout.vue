<template>
  <a-layout class="layout">
    <a-layout-header class="header">
      <div class="left"><LogoBox></LogoBox></div>
      <div class="right">
        <UserProfile />
      </div>
    </a-layout-header>
    <a-layout-content class="main">
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </a-layout-content>
    <a-layout-footer class="footer">
      <div class="footer-inner">
        <div class="footer-Info">
          <a-row :gutter="16" style="margin-left: -8px; margin-right: -8px">
            <a-col :span="6">
              <div class="footer-item">
                <h2>慕课乐高</h2>
                <p>购买课程</p>
                <p>联系我们</p>
                <p>关注我们</p>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="footer-item">
                <h2>设计制作帮助</h2>
                <p>关于我们</p>
                <p>联系我们</p>
                <p>关注我们</p>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="footer-item">
                <h2>审核问题</h2>
                <p>关于我们</p>
                <p>联系我们</p>
                <p>关注我们</p>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="footer-item">
                <h2>其他问题</h2>
                <p>关于我们</p>
                <p>联系我们</p>
                <p>关注我们</p>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>

      <div class="footer-bottom">
        <span> © 慕课网（imooc.com）版权所有 | </span>
        <span>
          <ul>
            <li>
              <a href="#"> 津ICP备20000929号-2 </a>
            </li>
            <li>
              <a href="#"> 购买课程 </a>
            </li>
            <li>
              <a href="#"> 作业和打卡 </a>
            </li>
            <li>
              <a href="#"> 联系我们 </a>
            </li>
            <li>
              <a href="#"> 帮助 </a>
            </li>
          </ul>
        </span>
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<script setup lang="ts">
// 组件已通过 configAntD.ts 全局注册，无需导入
import LogoBox from "@/components/Logo.vue";
import UserProfile from "@/components/UserProfile.vue";
</script>

<style scoped lang="scss">
@use "./MainLayout.module.scss" as *;
</style>
