{"name": "poster-craft", "version": "0.1.0", "private": true, "type": "commonjs", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:analyze": "cross-env ANALYZE_MODE=true vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:watch": "vue-cli-service test:unit --watch", "lint": "vue-cli-service lint", "mock": "nodemon ./fake-server/mockServer.js", "h5": "cd h5-server && npm start", "h5:dev": "cd h5-server && npm run dev", "h5:install": "cd h5-server && node install.js"}, "dependencies": {"@ant-design-vue/use": "^0.0.1-alpha.10", "@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "clipboard": "^2.0.11", "core-js": "^3.8.3", "cropperjs": "^1.6.2", "file-saver": "^2.0.5", "flush-promises": "^1.0.2", "hotkeys-js": "^3.13.14", "html2canvas": "^1.4.1", "json-server": "^0.17.4", "jsonwebtoken": "^9.0.2", "lego-components": "^0.1.7", "lodash-es": "^4.17.21", "multer": "^1.4.4", "path-to-regexp": "^8.2.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "rgb-hex": "^4.1.0", "uuid": "^11.1.0", "vue": "^3.3.0", "vue-router": "^4.0.13", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@types/clipboard": "^2.0.1", "@types/file-saver": "^2.0.7", "@types/jest": "^27.0.1", "@types/lodash-es": "^4.17.12", "@types/vuedraggable": "^2.23.2", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-plugin-jsx": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "@vue/runtime-core": "^3.5.13", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0-alpha.1", "babel-jest": "^27.0.6", "babel-loader": "^10.0.0", "babel-preset-vue-app": "^2.0.0", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "jest": "^27.0.5", "nodemon": "^3.1.10", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "ts-jest": "^27.0.4", "typescript": "^4.9.5"}, "jest": {"watchPathIgnorePatterns": ["node_modules"]}}