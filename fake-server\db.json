{"posts": [{"id": "1", "title": "a title", "views": 100}, {"id": "2", "title": "another title", "views": 200}], "comments": [{"id": "1", "body": "some comment", "postId": "1"}], "templates": [{"id": "1", "uuid": "8e55", "title": "意志力", "desc": "未命名作品", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "136****5632", "coverImg": "http://static-dev.imooc-lego.com/upload-files/screenshot-036563.png", "isTemplate": true, "status": 2}, {"id": "2", "uuid": "8e55", "title": "意志力2", "desc": "未命名作品", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "https://static.imooc-lego.com/upload-files/screenshot-649919.png", "isTemplate": true, "status": 2}, {"id": 1752114113300, "uuid": "8e55", "title": "大山深处的美好", "desc": "心灵的港湾", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "http://localhost:3000/uploads/1752114048121.png", "isTemplate": true, "status": 2, "copiedCount": 4, "createdAt": "2025-06-30T12:05:58.740Z", "updatedAt": "2025-07-10T02:21:53.300Z", "user": {"gender": "0", "userName": "185****5632", "nickName": "viking3", "picture": "https://static.imooc-lego.com/upload-files/screenshot-649919.png"}, "content": {"components": [{"id": "comp-1", "name": "l-text", "layerName": "标题", "props": {"text": "Hello", "fontSize": "20px", "color": "#f5222d", "top": "153.39288330078125px", "left": "145.64288330078125px"}}, {"id": "3c9c1f58-ac1c-4b7d-8e00-68830ddd3d5a", "name": "l-text", "props": {"text": "按钮内容", "fontSize": "14px", "fontFamily": "", "fontWeight": "normal", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "textAlign": "left", "color": "#fff", "backgroundColor": "#1890ff", "tag": "button", "actionType": "", "url": "", "height": "", "width": "", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "4px", "paddingBottom": "4px", "borderStyle": "none", "borderColor": "#1890ff", "borderWidth": "1px", "borderRadius": "4px", "boxShadow": "0 0 0 rgb(244, 238, 238)", "opacity": "1", "position": "absolute", "left": "223.64288330078125px", "top": "151.7857208251953px", "right": "0"}, "layerName": "图层2"}], "props": {"backgroundColor": "green", "backgroundRepeat": "no-repeat", "backgroundSize": "cover", "height": "800px"}}, "publishedAt": "2025-07-10T02:21:53.300Z", "channels": [{"id": 4, "name": "小红书", "workId": 71, "status": 1}, {"id": 1752071515078, "name": "知乎", "workId": 71, "status": 1}, {"id": 1752113379804, "name": "微信", "workId": 71, "status": 1}], "originalWorkId": 71}, {"id": 1752114122655, "uuid": "8e55", "title": "大山深处的美好", "desc": "心灵的港湾", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "http://localhost:3000/uploads/1752114048121.png", "isTemplate": true, "status": 2, "copiedCount": 4, "createdAt": "2025-06-30T12:05:58.740Z", "updatedAt": "2025-07-10T02:22:02.655Z", "user": {"gender": "0", "userName": "185****5632", "nickName": "viking3", "picture": "https://static.imooc-lego.com/upload-files/screenshot-649919.png"}, "content": {"components": [{"id": "comp-1", "name": "l-text", "layerName": "标题", "props": {"text": "Hello", "fontSize": "20px", "color": "#f5222d", "top": "153.39288330078125px", "left": "145.64288330078125px"}}, {"id": "3c9c1f58-ac1c-4b7d-8e00-68830ddd3d5a", "name": "l-text", "props": {"text": "按钮内容", "fontSize": "14px", "fontFamily": "", "fontWeight": "normal", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "textAlign": "left", "color": "#fff", "backgroundColor": "#1890ff", "tag": "button", "actionType": "", "url": "", "height": "", "width": "", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "4px", "paddingBottom": "4px", "borderStyle": "none", "borderColor": "#1890ff", "borderWidth": "1px", "borderRadius": "4px", "boxShadow": "0 0 0 rgb(244, 238, 238)", "opacity": "1", "position": "absolute", "left": "223.64288330078125px", "top": "151.7857208251953px", "right": "0"}, "layerName": "图层2"}], "props": {"backgroundColor": "green", "backgroundRepeat": "no-repeat", "backgroundSize": "cover", "height": "800px"}}, "publishedAt": "2025-07-10T02:22:02.655Z", "channels": [{"id": 4, "name": "小红书", "workId": 71, "status": 1}, {"id": 1752071515078, "name": "知乎", "workId": 71, "status": 1}, {"id": 1752113379804, "name": "微信", "workId": 71, "status": 1}], "originalWorkId": 71}, {"id": 1752114328230, "uuid": "8e55", "title": "大山深处的美好", "desc": "心灵的港湾", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "http://localhost:3000/uploads/1752114319673.png", "isTemplate": true, "status": 2, "copiedCount": 4, "createdAt": "2025-06-30T12:05:58.740Z", "updatedAt": "2025-07-10T02:25:28.230Z", "user": {"gender": "0", "userName": "185****5632", "nickName": "viking3", "picture": "https://static.imooc-lego.com/upload-files/screenshot-649919.png"}, "content": {"components": [{"id": "comp-1", "name": "l-text", "layerName": "标题", "props": {"text": "Hello", "fontSize": "20px", "color": "#f5222d", "top": "153.39288330078125px", "left": "145.64288330078125px"}}, {"id": "3c9c1f58-ac1c-4b7d-8e00-68830ddd3d5a", "name": "l-text", "props": {"text": "按钮内容", "fontSize": "14px", "fontFamily": "", "fontWeight": "normal", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "textAlign": "left", "color": "#fff", "backgroundColor": "#1890ff", "tag": "button", "actionType": "", "url": "", "height": "", "width": "", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "4px", "paddingBottom": "4px", "borderStyle": "none", "borderColor": "#1890ff", "borderWidth": "1px", "borderRadius": "4px", "boxShadow": "0 0 0 rgb(244, 238, 238)", "opacity": "1", "position": "absolute", "left": "223.64288330078125px", "top": "151.7857208251953px", "right": "0"}, "layerName": "图层2"}], "props": {"backgroundColor": "green", "backgroundRepeat": "no-repeat", "backgroundSize": "cover", "height": "800px"}}, "publishedAt": "2025-07-10T02:25:28.230Z", "channels": [{"id": 4, "name": "小红书", "workId": 71, "status": 1}, {"id": 1752071515078, "name": "知乎", "workId": 71, "status": 1}, {"id": 1752113379804, "name": "微信", "workId": 71, "status": 1}], "originalWorkId": 71}], "works": [{"id": "1", "uuid": "8e55", "title": "意志力", "desc": "未命名作品", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "136****5632", "coverImg": "http://static-dev.imooc-lego.com/upload-files/screenshot-036563.png", "isTemplate": true, "status": 2, "channels": [{"id": 1, "name": "微信朋友圈", "workId": 1, "status": 1}, {"id": 2, "name": "微博", "workId": 1, "status": 1}]}, {"id": "2", "uuid": "8e55", "title": "意志力2", "desc": "未命名作品", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "https://static.imooc-lego.com/upload-files/screenshot-649919.png", "isTemplate": true, "status": 2, "channels": [{"id": 3, "name": "抖音", "workId": 2, "status": 1}]}, {"id": 1751285158740, "title": "作品x", "desc": "啦啦啦", "coverImg": "coverImg: 'https://vivion-377477.postman.co/workspace/vivion's-Workspace~5ac785d7-2845-43b2-8b68-9405643ee784/request/45679852-1e322ad3-23ae-4e04-b831-41a239c39578?action=share&source=copy-link&creator=45679852&ctx=documentation'", "copiedCount": 0, "isTemplate": false, "createdAt": "2025-06-30T12:05:58.740Z", "updatedAt": "2025-06-30T12:05:58.740Z", "channels": []}, {"id": 71, "uuid": "8e55", "title": "大山深处的美好", "desc": "心灵的港湾", "contentId": "5fa128349fa7b1005c9f0541", "publishContentId": "5fa128749fa7b1005c9f0542", "author": "185****5632", "coverImg": "http://localhost:3000/uploads/1752114319673.png", "isTemplate": true, "status": 2, "copiedCount": 4, "createdAt": "2025-06-30T12:05:58.740Z", "updatedAt": "2025-07-10T02:25:28.230Z", "user": {"gender": "0", "userName": "185****5632", "nickName": "viking3", "picture": "https://static.imooc-lego.com/upload-files/screenshot-649919.png"}, "content": {"components": [{"id": "3c9c1f58-ac1c-4b7d-8e00-68830ddd3d5a", "name": "l-text", "props": {"text": "按钮内容", "fontSize": "14px", "fontFamily": "", "fontWeight": "normal", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "textAlign": "left", "color": "#fff", "backgroundColor": "#1890ff", "tag": "button", "actionType": "", "url": "", "height": "", "width": "", "paddingLeft": "10px", "paddingRight": "10px", "paddingTop": "4px", "paddingBottom": "4px", "borderStyle": "none", "borderColor": "#1890ff", "borderWidth": "1px", "borderRadius": "4px", "boxShadow": "0 0 0 rgb(244, 238, 238)", "opacity": "1", "position": "absolute", "left": "199.857177734375px", "top": "150.70538330078125px", "right": "0"}, "layerName": "图层2"}, {"id": "7253a202-9865-4889-8445-a2438a4ec9c7", "name": "l-image", "props": {"src": "http://localhost:3000/uploads/1752476338015.jpg", "actionType": "", "url": "", "height": "", "width": "200px", "paddingLeft": "0px", "paddingRight": "0px", "paddingTop": "0px", "paddingBottom": "0px", "borderStyle": "none", "borderColor": "#000", "borderWidth": "0", "borderRadius": "0", "boxShadow": "0 0 0 #000000", "opacity": "1", "position": "absolute", "left": "81.21429443359375px", "top": "24.919647216796875px", "right": "0"}, "layerName": "图层3"}, {"id": "3a3736e6-6cb7-46ec-a840-d94c037159a6", "name": "l-text", "props": {"text": "hello", "fontSize": "24px", "fontFamily": "", "fontWeight": "bold", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "textAlign": "left", "color": "#f5222d", "backgroundColor": "", "tag": "p", "actionType": "", "url": "", "height": "", "width": "", "paddingLeft": "0px", "paddingRight": "0px", "paddingTop": "0px", "paddingBottom": "0px", "borderStyle": "none", "borderColor": "#000", "borderWidth": "0", "borderRadius": "0", "boxShadow": "0 0 0 #000000", "opacity": "1", "position": "absolute", "left": "119.2857666015625px", "top": "150.4285888671875px", "right": "0"}, "layerName": "图层4"}], "props": {"backgroundColor": "green", "backgroundRepeat": "no-repeat", "backgroundSize": "cover", "height": "800px"}}, "publishedAt": "2025-07-10T02:25:28.230Z", "channels": [{"id": 4, "name": "小红书", "workId": 71, "status": 1}, {"id": 1752071515078, "name": "知乎", "workId": 71, "status": 1}, {"id": 1752113379804, "name": "微信", "workId": 71, "status": 1}]}]}