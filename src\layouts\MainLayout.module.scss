$header-height: 64px;
$header-background-color: #0c141cf6;

.layout,
.main,
.footer,
body,
html {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .header {
    height: $header-height;
    width: 100%;
    display: flex;
    padding: 0 24px;
    z-index: 1;
    background-color: $header-background-color;
    box-shadow: #0000001a 0 12px 15px 0;
    justify-content: space-between;
    align-items: center;

    .login-button {
      margin-left: 20px;
    }
  }

  .main {
    flex: 1;
  }

  .footer {
    background-color: #333;
    color: #999;
    padding: 24px 0;

    .footer-inner {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
      padding: 24px 0 10px 0;
    }

    .footer-Info {
      margin-bottom: 20px;
      position: relative;
      color: #fff;
      &::after {
        content: "";
        display: block;
        width: 100%;
        height: 1px;
        background-color: #999;
        opacity: 0.4;
        margin-top: 24px;
      }
    }

    .footer-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      h2 {
        font-size: 20px;
        font-weight: 500;
        color: #fff;
      }
      p {
        font-size: 16px;
        line-height: 33px;
        color: #999;
        text-align: left;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
      }
    }

    .footer-bottom {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #999;

      span {
        margin-right: 12px;
      }
      ul {
        display: flex;
        margin: 0;
        padding: 0;
        li {
          list-style: none;
          margin-right: 12px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
      a {
        color: #999;
        text-decoration: none;
        transition: color 0.2s;
        &:hover {
          color: #fff;
        }
      }
    }
  }
}

@media (max-width: 900px) {
  .main-container {
    flex-direction: column;
    align-items: stretch;
  }
  .left-container,
  .right-container {
    width: 100%;
    margin-bottom: 16px;
  }
  .footer-inner {
    flex-direction: column;
    align-items: flex-start;
  }
}
